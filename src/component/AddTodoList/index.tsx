import React from 'react'

interface addTodoProps {
  addTodo:(text:string)=>void
}
function AddTodoList({addTodo}:addTodoProps) {
  const [text, setText] = React.useState('')
  const handleSubmit= (e:React.FormEvent<HTMLFormElement>)=>{
    e.preventDefault()
    if(!text.trim()){

     return alert('请输入待办事项')
    }
    console.log(text);
    addTodo(text)
    setText('')
  }
  return (
    <div>
         <form action="" onSubmit={handleSubmit}>
            <input value={text} type="text" placeholder="请输入待办事项" className="input" onChange={(e)=>setText(e.target.value)}></input>
            <button>添加</button>
        </form>
    </div>
  )
}

export default AddTodoList
