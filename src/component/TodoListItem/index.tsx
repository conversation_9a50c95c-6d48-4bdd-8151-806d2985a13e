import React from 'react'
import type { TodoListItemPropsObj } from '../../types'

function TodoListItem({ item, deleteTodo, toggleTodo }: TodoListItemPropsObj) {
  console.log(item);

  return (
    <div style={{ textDecoration: item.isFinished ? "line-through" : "none" }}>
      <span>{item.content}</span>
      <button onClick={() => toggleTodo(item.id)}>切换</button>
      <button onClick={() => deleteTodo(item.id)}>删除</button>

    </div>
  )
}

export default TodoListItem
