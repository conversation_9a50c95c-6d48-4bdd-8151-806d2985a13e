
import { type TodoListItemProps } from "../../types";
import TodoListItem from "../TodoListItem";
// interface State {
function TodoList({todos,deleteTodo,toggleTodo}:TodoListItemProps) {
  return (
    <div>
        <ul>
          {todos.map((item) => (
            <TodoListItem key={item.id} item={item} deleteTodo={deleteTodo} toggleTodo={toggleTodo} />
          ))}
        </ul>
    </div>
  )
}

export default TodoList
