import React from 'react'
import TodoList from './component/TodoList'
import AddTodoList from './component/AddTodoList'
import TodoListFoot from './component/TodoListFoot'
import { type TodoListItem } from './types'
function App() {
  const [todos, setTodos] = React.useState<TodoListItem[]>([])
  const [filter,setFilter] = React.useState<string>('')
  const addTodo = (text: string) => {
    const newTodos ={
      id: Date.now(),
      content: text,
      isFinished: false
    }
    setTodos([...todos, newTodos])
  }
  const deleteTodo = (id: number) => {
    setTodos(todos.filter(todo => todo.id !== id))
  }
  const toggleTodo = (id: number) => {
    setTodos(todos.map(todo => todo.id === id ? {...todo, isFinished: !todo.isFinished} : todo))
  }
  const getFilteredTodos = () => {
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.isFinished)
      case 'completed':
        return todos.filter(todo => todo.isFinished)
      default:
        return todos
    }
  }
  return (
    <div >
      <h1>Todolist</h1>
      <AddTodoList addTodo={addTodo} />
      <TodoList  todos={getFilteredTodos()} deleteTodo={deleteTodo}  toggleTodo={toggleTodo} />
      <TodoListFoot  setFilter={setFilter} />
    </div>
  )
}

export default App
